# Mika 寄售商城 - 企业级单页网站

这是一个与Telegram寄售机器人功能完全相同的企业级单页网站，支持PHP 7.4和Nginx环境。

## 功能特性

- 🏪 **商户店铺展示** - 完整的商户信息和店铺管理
- 📦 **商品浏览购买** - 商品列表、详情页面和购买流程
- 💳 **多种支付方式** - 支持微信支付和支付宝
- 🚀 **自动发货系统** - 支付成功后自动发货
- 📱 **响应式设计** - 完美支持移动端和桌面端
- 🔒 **安全可靠** - 企业级安全配置和防护
- ⚡ **高性能优化** - Gzip压缩、静态资源缓存等

## 技术栈

- **后端**: PHP 7.4
- **Web服务器**: Nginx
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **样式框架**: 自定义响应式CSS
- **图标**: Font Awesome 6.0
- **API**: 与现有机器人API完全兼容

## 文件结构

```
jishou/
├── index.php              # 主页面文件
├── config.php             # 配置文件
├── nginx.conf             # Nginx配置文件
├── assets/
│   ├── css/
│   │   └── style.css      # 主样式文件
│   └── js/
│       └── main.js        # 主JavaScript文件
├── templates/
│   ├── homepage.php       # 首页模板
│   ├── merchant-shop.php  # 商户店铺模板
│   └── product-detail.php # 商品详情模板
└── README.md              # 说明文档
```

## 安装部署

### 环境要求

- PHP 7.4 或更高版本
- Nginx 1.18 或更高版本
- PHP扩展: json, curl, mbstring
- 确保后端API服务正常运行

### 部署步骤

1. **上传文件**
   ```bash
   # 将所有文件上传到Web服务器目录
   sudo cp -r jishou/ /var/www/html/
   sudo chown -R www-data:www-data /var/www/html/jishou/
   sudo chmod -R 755 /var/www/html/jishou/
   ```

2. **配置Nginx**
   ```bash
   # 复制Nginx配置文件
   sudo cp nginx.conf /etc/nginx/sites-available/mika-shop
   
   # 创建符号链接
   sudo ln -s /etc/nginx/sites-available/mika-shop /etc/nginx/sites-enabled/
   
   # 测试配置
   sudo nginx -t
   
   # 重新加载Nginx
   sudo systemctl reload nginx
   ```

3. **配置PHP-FPM**
   ```bash
   # 确保PHP-FPM正在运行
   sudo systemctl status php7.4-fpm
   sudo systemctl enable php7.4-fpm
   sudo systemctl start php7.4-fpm
   ```

4. **配置API地址**
   
   编辑 `config.php` 文件，确保API地址正确：
   ```php
   define('API_BASE_URL', 'http://127.0.0.1:7893/');
   ```

5. **测试网站**
   
   访问 `http://your-domain.com` 或 `http://localhost` 测试网站是否正常运行。

## 使用说明

### 访问方式

1. **首页访问**: `http://your-domain.com`
2. **商户店铺**: `http://your-domain.com?merchant=商户ID`
3. **商品详情**: `http://your-domain.com?product=商品ID`

### 主要功能

#### 1. 首页功能
- 平台介绍和特色展示
- 商户ID/商品ID快速访问
- 联系方式和帮助信息

#### 2. 商户店铺
- 店铺信息展示
- 商品列表浏览
- 商品快速购买
- 店铺分享功能

#### 3. 商品详情
- 详细商品信息
- 支付方式选择
- 购买流程处理
- 商品分享功能

#### 4. 订单处理
- 实时订单创建
- 支付状态检查
- 自动发货处理
- 投诉建议功能

## API接口

网站使用以下API接口（与机器人相同）：

- `get_merchant_info.php` - 获取商户信息
- `register_merchant.php` - 注册商户
- `get_product_list.php` - 获取商品列表
- `get_product_info.php` - 获取商品信息
- `create_order.php` - 创建订单
- `check_payment_status.php` - 检查支付状态
- `set_order_payment_status.php` - 设置订单支付状态

## 安全配置

### Nginx安全配置

- 隐藏敏感文件访问
- 限制请求频率和大小
- 安全头部配置
- SSL/TLS支持（可选）

### PHP安全配置

- 输入参数过滤和验证
- XSS和SQL注入防护
- 错误信息隐藏
- 会话安全配置

## 性能优化

- **Gzip压缩**: 减少传输数据大小
- **静态资源缓存**: 提升加载速度
- **CSS/JS优化**: 减少HTTP请求
- **图片优化**: 使用适当的图片格式

## 自定义配置

### 修改网站信息

编辑 `config.php` 文件：
```php
define('SITE_NAME', '您的网站名称');
define('SITE_DESCRIPTION', '您的网站描述');
define('COMPLAINT_URL', '您的投诉页面URL');
```

### 修改样式

编辑 `assets/css/style.css` 文件，可以自定义：
- 颜色主题
- 字体样式
- 布局结构
- 响应式断点

### 添加功能

可以在以下文件中添加新功能：
- `assets/js/main.js` - 前端交互功能
- `templates/` - 页面模板
- `index.php` - 后端逻辑

## 故障排除

### 常见问题

1. **页面无法访问**
   - 检查Nginx配置是否正确
   - 确认PHP-FPM服务状态
   - 检查文件权限设置

2. **API调用失败**
   - 确认API服务是否运行
   - 检查API地址配置
   - 查看错误日志

3. **样式显示异常**
   - 检查静态资源路径
   - 确认CSS文件权限
   - 清除浏览器缓存

### 日志查看

```bash
# Nginx访问日志
sudo tail -f /var/log/nginx/mika-shop-access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/mika-shop-error.log

# PHP错误日志
sudo tail -f /var/log/php7.4-fpm.log
```

## 维护更新

### 定期维护

1. 定期更新PHP和Nginx版本
2. 监控服务器性能和日志
3. 备份重要配置文件
4. 检查安全漏洞和更新

### 功能扩展

网站采用模块化设计，可以轻松扩展：
- 添加新的支付方式
- 集成更多第三方服务
- 增加数据统计功能
- 实现多语言支持

## 技术支持

如有问题，请联系：
- Telegram: @MikaJishouBot
- 官方频道: @MikaJiShou8
- 投诉建议: https://cloudshop.qnm6.top/tousu.html

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

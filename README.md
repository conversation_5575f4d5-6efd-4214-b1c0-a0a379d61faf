# Mika 寄售商城 - 企业级PHP网站

## 项目简介

这是一个企业级的寄售商城系统，功能与Telegram机器人完全相同，支持商户入驻、商品管理、订单处理、支付集成等功能。

## 技术栈

- **后端**: PHP 8.0+, MySQL 8.0+
- **前端**: HTML5, CSS3, JavaScript (ES6+), Bootstrap 5
- **Web服务器**: Nginx
- **支付**: 微信支付, 支付宝
- **其他**: Redis (缓存), JWT (认证)

## 功能特性

### 核心功能
- 🏪 商户管理系统
- 📦 商品管理系统  
- 🛒 订单管理系统
- 💳 支付集成 (微信支付/支付宝)
- 📊 数据统计分析
- 🔐 安全认证系统

### 商户功能
- 商户注册与认证
- 商品上架与管理
- 库存管理
- 订单处理
- 收益统计
- 店铺装修

### 用户功能
- 商品浏览
- 购物车管理
- 订单支付
- 订单查询
- 商品分享

## 项目结构

```
mika-shop/
├── api/                    # API接口目录
│   ├── config/            # 配置文件
│   ├── controllers/       # 控制器
│   ├── models/           # 数据模型
│   ├── middleware/       # 中间件
│   └── routes/           # 路由定义
├── admin/                 # 管理后台
│   ├── assets/           # 静态资源
│   ├── pages/            # 页面文件
│   └── components/       # 组件
├── public/               # 前端公共目录
│   ├── assets/          # 静态资源
│   ├── css/             # 样式文件
│   ├── js/              # JavaScript文件
│   └── images/          # 图片资源
├── database/             # 数据库相关
│   ├── migrations/      # 数据库迁移
│   └── seeds/           # 数据填充
├── config/               # 全局配置
├── logs/                 # 日志文件
└── docs/                 # 文档
```

## 安装部署

### 环境要求
- PHP >= 8.0
- MySQL >= 8.0
- Nginx >= 1.18
- Redis >= 6.0

### 安装步骤
1. 克隆项目到服务器
2. 配置数据库连接
3. 导入数据库结构
4. 配置Nginx虚拟主机
5. 设置文件权限
6. 配置支付接口

详细安装说明请参考 `docs/installation.md`

## API文档

API文档请参考 `docs/api.md`

## 许可证

MIT License

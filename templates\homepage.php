<!-- 首页内容 -->
<div class="hero-section">
    <div class="hero-content">
        <h2 class="hero-title">
            <i class="fas fa-rocket"></i>
            欢迎使用 Mika 寄售商城
        </h2>
        <p class="hero-description">
            匿名、安全的寄售平台<br>
            一站式托管发卡服务，零门槛入驻
        </p>
        <div class="hero-features">
            <div class="feature">
                <i class="fas fa-clock"></i>
                <span>7x24h自动发货</span>
            </div>
            <div class="feature">
                <i class="fas fa-shield-alt"></i>
                <span>交易完全匿名</span>
            </div>
            <div class="feature">
                <i class="fas fa-handshake"></i>
                <span>售后稳定可靠</span>
            </div>
        </div>
    </div>
</div>

<!-- 功能介绍 -->
<div class="features-section">
    <h3 class="section-title">平台特色</h3>
    <div class="features-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-store"></i>
            </div>
            <h4>开设店铺</h4>
            <p>零门槛入驻，快速开设您的专属店铺，开始您的寄售之旅</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-box"></i>
            </div>
            <h4>商品管理</h4>
            <p>简单易用的商品管理系统，支持多种商品类型和库存管理</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <h4>多种支付</h4>
            <p>支持微信支付、支付宝等多种支付方式，满足不同用户需求</p>
        </div>
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-shipping-fast"></i>
            </div>
            <h4>自动发货</h4>
            <p>支付成功后自动发货，无需人工干预，提升用户体验</p>
        </div>
    </div>
</div>

<!-- 如何开始 -->
<div class="getting-started-section">
    <h3 class="section-title">如何开始</h3>
    <div class="steps">
        <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h4>访问店铺</h4>
                <p>通过商户ID访问店铺，或直接访问商品链接</p>
            </div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h4>选择商品</h4>
                <p>浏览商品列表，查看商品详情和价格信息</p>
            </div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h4>下单支付</h4>
                <p>选择支付方式，完成订单支付流程</p>
            </div>
        </div>
        <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
                <h4>自动发货</h4>
                <p>支付成功后系统自动发货，获取商品内容</p>
            </div>
        </div>
    </div>
</div>

<!-- 访问店铺表单 -->
<div class="access-shop-section">
    <h3 class="section-title">访问店铺</h3>
    <div class="access-form">
        <form id="accessShopForm" class="form">
            <div class="form-group">
                <label for="merchantId">商户ID</label>
                <input type="text" id="merchantId" name="merchant_id" placeholder="请输入商户ID" required>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
                访问店铺
            </button>
        </form>
        
        <div class="form-divider">
            <span>或</span>
        </div>
        
        <form id="accessProductForm" class="form">
            <div class="form-group">
                <label for="productId">商品ID</label>
                <input type="text" id="productId" name="product_id" placeholder="请输入商品ID" required>
            </div>
            <button type="submit" class="btn btn-secondary">
                <i class="fas fa-box-open"></i>
                查看商品
            </button>
        </form>
    </div>
</div>

<!-- 联系我们 -->
<div class="contact-section" id="about">
    <h3 class="section-title">联系我们</h3>
    <div class="contact-info">
        <div class="contact-item">
            <i class="fab fa-telegram"></i>
            <div>
                <h4>Telegram机器人</h4>
                <p>@<?php echo BOT_USERNAME; ?></p>
            </div>
        </div>
        <div class="contact-item">
            <i class="fas fa-bullhorn"></i>
            <div>
                <h4>官方通知频道</h4>
                <p>@MikaJiShou8</p>
            </div>
        </div>
        <div class="contact-item">
            <i class="fas fa-exclamation-circle"></i>
            <div>
                <h4>投诉建议</h4>
                <p><a href="<?php echo COMPLAINT_URL; ?>" target="_blank">点击投诉</a></p>
            </div>
        </div>
    </div>
</div>

<script>
// 访问店铺表单处理
document.getElementById('accessShopForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const merchantId = document.getElementById('merchantId').value.trim();
    if (merchantId) {
        window.location.href = `index.php?merchant=${encodeURIComponent(merchantId)}`;
    }
});

// 访问商品表单处理
document.getElementById('accessProductForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const productId = document.getElementById('productId').value.trim();
    if (productId) {
        window.location.href = `index.php?product=${encodeURIComponent(productId)}`;
    }
});
</script>

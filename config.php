<?php
/**
 * 企业级寄售网站配置文件
 * 与机器人API保持一致的配置
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// API配置
define('API_BASE_URL', 'http://127.0.0.1:7893/');
define('BOT_USERNAME', 'MikaJishouBot');

// 商户秘钥生成配置
define('MERCHANT_SECRET_KEY', 'yjsyjs_merchant_secret_key_2024');
define('MERCHANT_SALT', 'yjsyjs_salt_2024');

// 网站配置
define('SITE_NAME', 'Mika 寄售商城');
define('SITE_DESCRIPTION', '匿名、安全的寄售平台，一站式托管发卡服务，零门槛入驻');
define('COMPLAINT_URL', 'https://cloudshop.qnm6.top/tousu.html');
define('ADMIN_BASE_URL', 'https://cloudshop.qnm6.top/admin/index.php');

// 支付配置
define('PAYMENT_TIMEOUT', 1800); // 30分钟支付超时

/**
 * 生成商户秘钥（与Python版本保持一致）
 * @param string $merchant_id 商户ID
 * @return string 商户秘钥
 */
function generate_merchant_secret($merchant_id) {
    $key = MERCHANT_SECRET_KEY;
    $salt = MERCHANT_SALT;
    
    // 组合商户ID、密钥和盐值
    $combined = $merchant_id . $key . $salt;
    
    // 使用MD5生成32位字符串
    $hash_value = md5($combined);
    
    // 添加商户ID前缀，确保唯一性
    $prefix = substr($merchant_id, 0, 3);
    
    // 组合成最终的商户秘钥
    $merchant_secret = $prefix . '_' . $hash_value;
    
    return $merchant_secret;
}

/**
 * 调用API接口
 * @param string $endpoint API端点
 * @param array $params 参数
 * @return array|null API响应
 */
function call_api($endpoint, $params = []) {
    $url = API_BASE_URL . $endpoint;
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => [
                'User-Agent: Mika-Shop-Web/1.0',
                'Accept: application/json'
            ]
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        error_log("API调用失败: $url");
        return null;
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("API响应解析失败: " . json_last_error_msg());
        return null;
    }
    
    return $data;
}

/**
 * 获取商户信息
 * @param string $merchant_id 商户ID
 * @return array|null 商户信息
 */
function get_merchant_info($merchant_id) {
    return call_api('get_merchant_info.php', ['merchant_id' => $merchant_id]);
}

/**
 * 注册商户
 * @param string $merchant_id 商户ID
 * @return array|null 注册结果
 */
function register_merchant($merchant_id) {
    return call_api('register_merchant.php', ['merchant_id' => $merchant_id]);
}

/**
 * 获取商品列表
 * @param string $merchant_secret 商户秘钥
 * @return array|null 商品列表
 */
function get_product_list($merchant_secret) {
    return call_api('get_product_list.php', ['merchant_secret' => $merchant_secret]);
}

/**
 * 获取商品信息
 * @param string $product_id 商品ID
 * @return array|null 商品信息
 */
function get_product_info($product_id) {
    return call_api('get_product_info.php', ['product_id' => $product_id]);
}

/**
 * 创建订单
 * @param string $customer_contact 客户联系方式
 * @param string $product_id 商品ID
 * @param string $pay_type 支付方式
 * @return array|null 订单信息
 */
function create_order($customer_contact, $product_id, $pay_type = 'wxpay') {
    return call_api('create_order.php', [
        'customer_contact' => $customer_contact,
        'product_id' => $product_id,
        'pay_type' => $pay_type
    ]);
}

/**
 * 检查支付状态
 * @param string $order_id 订单ID
 * @return array|null 支付状态
 */
function check_payment_status($order_id) {
    return call_api('check_payment_status.php', ['order_id' => $order_id]);
}

/**
 * 设置订单支付状态
 * @param string $order_id 订单ID
 * @param string $payment_status 支付状态
 * @return array|null 设置结果
 */
function set_order_payment_status($order_id, $payment_status) {
    return call_api('set_order_payment_status.php', [
        'order_id' => $order_id,
        'payment_status' => $payment_status
    ]);
}

/**
 * 安全地获取GET参数
 * @param string $key 参数名
 * @param mixed $default 默认值
 * @return mixed 参数值
 */
function get_param($key, $default = '') {
    return isset($_GET[$key]) ? htmlspecialchars(trim($_GET[$key]), ENT_QUOTES, 'UTF-8') : $default;
}

/**
 * 安全地获取POST参数
 * @param string $key 参数名
 * @param mixed $default 默认值
 * @return mixed 参数值
 */
function post_param($key, $default = '') {
    return isset($_POST[$key]) ? htmlspecialchars(trim($_POST[$key]), ENT_QUOTES, 'UTF-8') : $default;
}

/**
 * 输出JSON响应
 * @param array $data 响应数据
 */
function json_response($data) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 格式化价格
 * @param float $price 价格
 * @return string 格式化后的价格
 */
function format_price($price) {
    return '¥' . number_format((float)$price, 2);
}

/**
 * 格式化时间
 * @param string $datetime 时间字符串
 * @return string 格式化后的时间
 */
function format_datetime($datetime) {
    return date('Y-m-d H:i:s', strtotime($datetime));
}

/**
 * 生成随机字符串
 * @param int $length 长度
 * @return string 随机字符串
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 开启会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

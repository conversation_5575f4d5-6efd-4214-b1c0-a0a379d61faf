# Nginx配置文件 - 企业级寄售网站
# 适用于PHP 7.4和Nginx环境

server {
    listen 80;
    listen [::]:80;
    
    # 服务器名称 - 请根据实际域名修改
    server_name localhost mika-shop.local;
    
    # 网站根目录
    root /var/www/html/jishou;
    
    # 默认首页文件
    index index.php index.html index.htm;
    
    # 字符集
    charset utf-8;
    
    # 访问日志和错误日志
    access_log /var/log/nginx/mika-shop-access.log;
    error_log /var/log/nginx/mika-shop-error.log;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 主要位置配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP文件处理
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        
        # PHP-FPM配置 - 根据实际PHP-FPM配置调整
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        # 或者使用TCP连接: fastcgi_pass 127.0.0.1:9000;
        
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP执行超时设置
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        
        # 缓冲区设置
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 隐藏敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问配置文件
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问备份文件
    location ~ \.(bak|backup|old|orig|original|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # robots.txt
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }
    
    # favicon.ico
    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # 限制请求频率 - 防止DDoS攻击
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/html/errors;
        internal;
    }
    
    location = /50x.html {
        root /var/www/html/errors;
        internal;
    }
}

# HTTPS配置 (可选)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     
#     server_name mika-shop.local;
#     
#     # SSL证书配置
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=63072000" always;
#     
#     # 其他配置与HTTP相同
#     root /var/www/html/jishou;
#     index index.php index.html index.htm;
#     
#     # ... 其他location配置 ...
# }

# HTTP到HTTPS重定向 (如果使用HTTPS)
# server {
#     listen 80;
#     listen [::]:80;
#     server_name mika-shop.local;
#     return 301 https://$server_name$request_uri;
# }

# 全局配置建议
# 在nginx.conf的http块中添加以下配置:

# # 限制请求频率
# limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;
# 
# # 连接限制
# limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
# limit_conn conn_limit_per_ip 20;
# 
# # 隐藏Nginx版本
# server_tokens off;
# 
# # 客户端缓冲区大小
# client_body_buffer_size 128k;
# client_header_buffer_size 1k;
# large_client_header_buffers 4 4k;
# 
# # 超时设置
# client_body_timeout 12;
# client_header_timeout 12;
# keepalive_timeout 15;
# send_timeout 10;

# 使用说明:
# 1. 将此配置文件保存为 /etc/nginx/sites-available/mika-shop
# 2. 创建符号链接: sudo ln -s /etc/nginx/sites-available/mika-shop /etc/nginx/sites-enabled/
# 3. 测试配置: sudo nginx -t
# 4. 重新加载Nginx: sudo systemctl reload nginx
# 5. 确保PHP-FPM正在运行: sudo systemctl status php7.4-fpm

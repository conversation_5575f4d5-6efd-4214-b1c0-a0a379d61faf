<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能演示 - Mika 寄售商城</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .demo-card {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .demo-card:hover {
            border-color: #007bff;
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }
        
        .demo-icon {
            font-size: 3rem;
            color: #007bff;
            margin-bottom: 1rem;
        }
        
        .demo-title {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #343a40;
        }
        
        .demo-description {
            color: #6c757d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .demo-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .demo-link:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-store"></i>
                    Mika 寄售商城 - 功能演示
                </h1>
                <nav class="nav">
                    <a href="index.php" class="nav-link">返回首页</a>
                    <a href="#features" class="nav-link">功能展示</a>
                    <a href="#examples" class="nav-link">使用示例</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
        <div class="container">
            <!-- 介绍部分 -->
            <div class="hero-section">
                <div class="hero-content">
                    <h2 class="hero-title">
                        <i class="fas fa-rocket"></i>
                        企业级寄售网站演示
                    </h2>
                    <p class="hero-description">
                        与Telegram机器人功能完全相同的Web版本<br>
                        支持商户管理、商品展示、在线购买、自动发货
                    </p>
                </div>
            </div>

            <!-- 功能展示 -->
            <div class="demo-section" id="features">
                <h3 class="section-title">核心功能展示</h3>
                <div class="demo-grid">
                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <h4 class="demo-title">首页展示</h4>
                        <p class="demo-description">
                            平台介绍、功能特色、快速访问入口
                        </p>
                        <a href="index.php" class="demo-link">
                            <i class="fas fa-eye"></i>
                            查看首页
                        </a>
                    </div>

                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <h4 class="demo-title">商户店铺</h4>
                        <p class="demo-description">
                            店铺信息、商品列表、快速购买功能
                        </p>
                        <a href="index.php?merchant=demo123" class="demo-link">
                            <i class="fas fa-shopping-bag"></i>
                            访问店铺
                        </a>
                    </div>

                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <h4 class="demo-title">商品详情</h4>
                        <p class="demo-description">
                            详细信息、支付选择、购买流程
                        </p>
                        <a href="index.php?product=demo001" class="demo-link">
                            <i class="fas fa-info-circle"></i>
                            查看商品
                        </a>
                    </div>

                    <div class="demo-card">
                        <div class="demo-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4 class="demo-title">响应式设计</h4>
                        <p class="demo-description">
                            完美适配手机、平板、桌面设备
                        </p>
                        <a href="#" onclick="toggleMobileView()" class="demo-link">
                            <i class="fas fa-mobile"></i>
                            切换视图
                        </a>
                    </div>
                </div>
            </div>

            <!-- 使用示例 -->
            <div class="demo-section" id="examples">
                <h3 class="section-title">使用示例</h3>
                
                <div class="example-item">
                    <h4><i class="fas fa-link"></i> URL访问方式</h4>
                    <p>通过不同的URL参数访问不同的页面：</p>
                    
                    <div class="code-block">
                        <div><span class="highlight">首页</span>: http://your-domain.com/</div>
                        <div><span class="highlight">商户店铺</span>: http://your-domain.com/?merchant=商户ID</div>
                        <div><span class="highlight">商品详情</span>: http://your-domain.com/?product=商品ID</div>
                    </div>
                </div>

                <div class="example-item">
                    <h4><i class="fas fa-cog"></i> API集成</h4>
                    <p>网站使用与机器人相同的API接口：</p>
                    
                    <div class="code-block">
                        • get_merchant_info.php - 获取商户信息<br>
                        • get_product_list.php - 获取商品列表<br>
                        • get_product_info.php - 获取商品详情<br>
                        • create_order.php - 创建订单<br>
                        • check_payment_status.php - 检查支付状态
                    </div>
                </div>

                <div class="example-item">
                    <h4><i class="fas fa-credit-card"></i> 支付流程</h4>
                    <p>完整的购买和支付流程：</p>
                    
                    <div class="code-block">
                        1. 选择商品 → 2. 输入联系方式 → 3. 选择支付方式<br>
                        4. 创建订单 → 5. 完成支付 → 6. 自动发货
                    </div>
                </div>
            </div>

            <!-- 技术特性 -->
            <div class="demo-section">
                <h3 class="section-title">技术特性</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h4>现代化技术栈</h4>
                        <p>PHP 7.4 + Nginx + HTML5 + CSS3 + ES6+</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>企业级安全</h4>
                        <p>输入验证、XSS防护、安全头部配置</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h4>性能优化</h4>
                        <p>Gzip压缩、静态缓存、资源优化</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>响应式设计</h4>
                        <p>完美适配各种设备和屏幕尺寸</p>
                    </div>
                </div>
            </div>

            <!-- 部署说明 -->
            <div class="demo-section">
                <h3 class="section-title">快速部署</h3>
                <p>按照以下步骤即可快速部署网站：</p>
                
                <div class="code-block">
                    # 1. 上传文件到服务器<br>
                    sudo cp -r jishou/ /var/www/html/<br><br>
                    
                    # 2. 配置Nginx<br>
                    sudo cp nginx.conf /etc/nginx/sites-available/mika-shop<br>
                    sudo ln -s /etc/nginx/sites-available/mika-shop /etc/nginx/sites-enabled/<br><br>
                    
                    # 3. 重启服务<br>
                    sudo systemctl reload nginx<br>
                    sudo systemctl restart php7.4-fpm
                </div>
                
                <p>详细部署说明请参考 <strong>README.md</strong> 文件。</p>
            </div>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>关于项目</h3>
                    <p>企业级寄售网站，与Telegram机器人功能完全相同，支持商户管理、商品展示、在线购买等功能。</p>
                </div>
                <div class="footer-section">
                    <h3>技术支持</h3>
                    <p><i class="fab fa-telegram"></i> @MikaJishouBot</p>
                    <p><i class="fas fa-envelope"></i> 官方频道: @MikaJiShou8</p>
                </div>
                <div class="footer-section">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="index.php">返回首页</a></li>
                        <li><a href="README.md">部署文档</a></li>
                        <li><a href="https://cloudshop.qnm6.top/tousu.html">投诉建议</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Mika 寄售商城. 演示版本.</p>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileView() {
            const viewport = document.querySelector('meta[name="viewport"]');
            const currentContent = viewport.getAttribute('content');
            
            if (currentContent.includes('width=device-width')) {
                viewport.setAttribute('content', 'width=375');
                alert('已切换到移动端视图（375px宽度）');
            } else {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
                alert('已切换到响应式视图');
            }
        }
    </script>
</body>
</html>

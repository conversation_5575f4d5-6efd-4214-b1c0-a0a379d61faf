/* 企业级寄售网站样式 */

/* 基础重置和变量 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --border-color: #dee2e6;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: var(--white);
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

.logo i {
    margin-right: 0.5rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color);
}

/* 主要内容区域 */
.main {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-disabled {
    background: var(--secondary-color);
    color: var(--white);
    cursor: not-allowed;
    opacity: 0.6;
}

/* 首页样式 */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: var(--white);
    padding: 4rem 0;
    text-align: center;
    border-radius: var(--border-radius);
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.feature i {
    font-size: 1.2rem;
}

/* 功能介绍 */
.features-section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.feature-card p {
    color: var(--secondary-color);
    line-height: 1.6;
}

/* 步骤说明 */
.getting-started-section {
    margin-bottom: 3rem;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.step-number {
    background: var(--primary-color);
    color: var(--white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content h4 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.step-content p {
    color: var(--secondary-color);
}

/* 表单样式 */
.access-shop-section {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 3rem;
}

.access-form {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: end;
}

.form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
    font-weight: 500;
}

/* 联系信息 */
.contact-section {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    font-size: 2rem;
    color: var(--primary-color);
}

.contact-item h4 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.contact-item p {
    color: var(--secondary-color);
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* 错误和成功消息 */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid #f5c6cb;
}

.error-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid #c3e6cb;
}

/* 底部样式 */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section p {
    color: #adb5bd;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    color: #adb5bd;
    margin-bottom: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #495057;
    color: #adb5bd;
}

/* 商户店铺样式 */
.shop-header {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.shop-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex: 1;
}

.shop-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0;
}

.shop-details h2 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.shop-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.shop-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.shop-id {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.shop-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* 商品网格 */
.products-section {
    margin-bottom: 3rem;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.product-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.product-card.out-of-stock {
    opacity: 0.7;
}

.product-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.product-name {
    font-size: 1.2rem;
    color: var(--dark-color);
    margin: 0;
    flex: 1;
}

.product-status {
    flex-shrink: 0;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.in-stock {
    background: #d4edda;
    color: #155724;
}

.status-badge.sold-out {
    background: #f8d7da;
    color: #721c24;
}

.product-body {
    padding: 0 1.5rem 1rem;
}

.product-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.product-actions {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.product-actions .btn {
    flex: 1;
    min-width: 120px;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-state h4 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

/* 店铺统计 */
.shop-stats {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--dark-color);
}

.stat-label {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        gap: 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
    }

    .access-form {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-divider {
        order: 1;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .steps {
        grid-template-columns: 1fr;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .shop-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .shop-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .shop-actions {
        width: 100%;
        justify-content: center;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 商品详情页面样式 */
.product-detail {
    margin-bottom: 3rem;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb .separator {
    color: var(--secondary-color);
}

.breadcrumb .current {
    color: var(--secondary-color);
}

.product-info {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.product-main {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.product-image {
    width: 150px;
    height: 150px;
    background: var(--light-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
    float: left;
    margin-right: 2rem;
}

.product-title {
    font-size: 2rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.product-meta {
    margin-bottom: 2rem;
}

.meta-item {
    display: flex;
    margin-bottom: 0.5rem;
    gap: 1rem;
}

.meta-item .label {
    font-weight: 500;
    color: var(--dark-color);
    min-width: 100px;
}

.meta-item .value {
    color: var(--secondary-color);
}

.status-in-stock {
    color: var(--success-color) !important;
}

.status-sold-out {
    color: var(--danger-color) !important;
}

.product-price {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.price-label {
    font-size: 1rem;
    color: var(--secondary-color);
    margin-right: 1rem;
}

.price-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.product-description h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.product-description p {
    color: var(--secondary-color);
    line-height: 1.6;
}

/* 购买区域 */
.purchase-section {
    position: sticky;
    top: 100px;
}

.purchase-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.purchase-card h3 {
    margin-bottom: 1.5rem;
    color: var(--dark-color);
}

.sold-out-notice {
    text-align: center;
    padding: 2rem;
    color: var(--danger-color);
}

.sold-out-notice i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.purchase-form {
    margin-bottom: 2rem;
}

.form-help {
    color: var(--secondary-color);
    font-size: 0.8rem;
    margin-top: 0.3rem;
}

.payment-methods {
    margin-bottom: 2rem;
}

.payment-methods h4 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.payment-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.payment-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
    cursor: pointer;
    transition: var(--transition);
}

.payment-btn:hover {
    border-color: var(--primary-color);
    background: var(--light-color);
}

.payment-btn.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.payment-btn i {
    font-size: 2rem;
}

.purchase-summary {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-row.total {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--dark-color);
    border-top: 1px solid var(--border-color);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.product-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.product-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* 商户信息区域 */
.merchant-info-section {
    margin-bottom: 3rem;
}

.merchant-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 2rem;
}

.merchant-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0;
}

.merchant-details {
    flex: 1;
}

.merchant-name {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.merchant-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.merchant-actions {
    display: flex;
    gap: 1rem;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color);
    cursor: pointer;
    z-index: 1;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.close:hover {
    background: var(--light-color);
    color: var(--dark-color);
}

#modalBody {
    padding: 2rem;
}

/* 订单模态框内容 */
.order-info {
    margin-bottom: 2rem;
}

.order-info h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.order-details {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.order-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.order-item .label {
    font-weight: 500;
    color: var(--dark-color);
}

.order-item .value {
    color: var(--secondary-color);
}

.payment-info {
    text-align: center;
    margin-bottom: 2rem;
}

.payment-qr {
    width: 200px;
    height: 200px;
    background: var(--light-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem auto;
    font-size: 4rem;
    color: var(--secondary-color);
}

.payment-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.payment-actions .btn {
    min-width: 120px;
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-overlay.show {
    display: flex;
}

.loading-content {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--dark-color);
    font-weight: 500;
}

/* 支付结果样式 */
.payment-result {
    text-align: center;
    padding: 2rem;
}

.payment-result.success {
    color: var(--success-color);
}

.payment-result.error {
    color: var(--danger-color);
}

.payment-result i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.payment-result h3 {
    margin-bottom: 1rem;
}

.delivery-content {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-all;
    border: 1px solid var(--border-color);
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .product-info {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .purchase-section {
        position: static;
    }

    .product-image {
        float: none;
        margin: 0 auto 2rem;
    }

    .product-title {
        font-size: 1.5rem;
    }

    .price-value {
        font-size: 1.5rem;
    }

    .payment-options {
        grid-template-columns: 1fr;
    }

    .merchant-card {
        flex-direction: column;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .payment-actions {
        flex-direction: column;
    }

    .payment-actions .btn {
        width: 100%;
    }
}

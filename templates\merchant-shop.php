<?php
$merchant_info = $page_data['merchant_info'];
$products = $page_data['products'];
$merchant_id = get_param('merchant');
?>

<!-- 商户店铺页面 -->
<div class="shop-header">
    <div class="shop-info">
        <div class="shop-avatar">
            <i class="fas fa-store"></i>
        </div>
        <div class="shop-details">
            <h2 class="shop-name">
                <?php echo htmlspecialchars($merchant_info['shop_name'] ?? '未知商店'); ?>
            </h2>
            <p class="shop-description">
                <?php echo htmlspecialchars($merchant_info['shop_description'] ?? '暂无介绍'); ?>
            </p>
            <div class="shop-meta">
                <span class="shop-id">
                    <i class="fas fa-id-card"></i>
                    商户ID: <?php echo htmlspecialchars($merchant_id); ?>
                </span>
            </div>
        </div>
    </div>
    
    <div class="shop-actions">
        <a href="<?php echo ADMIN_BASE_URL; ?>?merchant_secret=<?php echo generate_merchant_secret($merchant_id); ?>" 
           class="btn btn-primary" target="_blank">
            <i class="fas fa-cog"></i>
            管理店铺
        </a>
        <button class="btn btn-secondary" onclick="shareShop('<?php echo $merchant_id; ?>')">
            <i class="fas fa-share"></i>
            分享店铺
        </button>
    </div>
</div>

<!-- 商品列表 -->
<div class="products-section">
    <h3 class="section-title">
        <i class="fas fa-box"></i>
        商品列表
    </h3>
    
    <?php if (empty($products)): ?>
        <div class="empty-state">
            <i class="fas fa-box-open"></i>
            <h4>该商户还没有商品</h4>
            <p>商户正在准备商品中，请稍后再来查看</p>
        </div>
    <?php else: ?>
        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <?php
                $product_id = $product['product_id'];
                $product_name = htmlspecialchars($product['product_name'] ?? '未知商品');
                $product_price = $product['product_price'] ?? '0.00';
                $stock_quantity = intval($product['stock_quantity'] ?? 0);
                $product_description = htmlspecialchars($product['product_description'] ?? '暂无描述');
                ?>
                
                <div class="product-card <?php echo $stock_quantity <= 0 ? 'out-of-stock' : ''; ?>">
                    <div class="product-header">
                        <h4 class="product-name"><?php echo $product_name; ?></h4>
                        <div class="product-status">
                            <?php if ($stock_quantity <= 0): ?>
                                <span class="status-badge sold-out">
                                    <i class="fas fa-times-circle"></i>
                                    售罄
                                </span>
                            <?php else: ?>
                                <span class="status-badge in-stock">
                                    <i class="fas fa-check-circle"></i>
                                    库存: <?php echo $stock_quantity; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="product-body">
                        <p class="product-description"><?php echo $product_description; ?></p>
                        <div class="product-price">
                            <?php echo format_price($product_price); ?>
                        </div>
                    </div>
                    
                    <div class="product-actions">
                        <button class="btn btn-outline" onclick="viewProduct('<?php echo $product_id; ?>')">
                            <i class="fas fa-eye"></i>
                            查看详情
                        </button>
                        
                        <?php if ($stock_quantity > 0): ?>
                            <button class="btn btn-primary" onclick="quickBuy('<?php echo $product_id; ?>', 'wxpay')">
                                <i class="fab fa-weixin"></i>
                                微信支付
                            </button>
                            <button class="btn btn-success" onclick="quickBuy('<?php echo $product_id; ?>', 'alipay')">
                                <i class="fab fa-alipay"></i>
                                支付宝
                            </button>
                        <?php else: ?>
                            <button class="btn btn-disabled" disabled>
                                <i class="fas fa-ban"></i>
                                已售罄
                            </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-outline" onclick="shareProduct('<?php echo $product_id; ?>')">
                            <i class="fas fa-share"></i>
                            分享
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- 店铺统计信息 -->
<div class="shop-stats">
    <div class="stats-grid">
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-box"></i>
            </div>
            <div class="stat-info">
                <div class="stat-number"><?php echo count($products); ?></div>
                <div class="stat-label">商品总数</div>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-info">
                <div class="stat-number">
                    <?php echo count(array_filter($products, function($p) { return intval($p['stock_quantity'] ?? 0) > 0; })); ?>
                </div>
                <div class="stat-label">有库存商品</div>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-info">
                <div class="stat-number">24/7</div>
                <div class="stat-label">自动发货</div>
            </div>
        </div>
        
        <div class="stat-item">
            <div class="stat-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stat-info">
                <div class="stat-number">100%</div>
                <div class="stat-label">匿名交易</div>
            </div>
        </div>
    </div>
</div>

<script>
// 查看商品详情
function viewProduct(productId) {
    window.location.href = `index.php?product=${productId}`;
}

// 快速购买
function quickBuy(productId, payType) {
    const customerContact = prompt('请输入您的联系方式（用于接收商品）:');
    if (!customerContact || customerContact.trim() === '') {
        alert('联系方式不能为空');
        return;
    }
    
    // 显示加载状态
    showLoading('正在创建订单...');
    
    // 创建订单
    fetch('index.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=create_order&product_id=${productId}&pay_type=${payType}&customer_contact=${encodeURIComponent(customerContact)}`
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.status === 'success') {
            showOrderModal(data.data);
        } else {
            alert('创建订单失败: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        alert('网络错误，请稍后重试');
        console.error('Error:', error);
    });
}

// 分享店铺
function shareShop(merchantId) {
    const shareUrl = `${window.location.origin}/index.php?merchant=${merchantId}`;
    const shareText = `💎 这是我的在线商城!7x24h自动发货、售后稳定,交易完全匿名,欢迎下单！\n\n🔗 店铺链接：${shareUrl}`;
    
    if (navigator.share) {
        navigator.share({
            title: '分享店铺',
            text: shareText,
            url: shareUrl
        });
    } else {
        copyToClipboard(shareText);
        alert('分享链接已复制到剪贴板');
    }
}

// 分享商品
function shareProduct(productId) {
    const shareUrl = `${window.location.origin}/index.php?product=${productId}`;
    const shareText = `💎 推荐好物，自动发货，欢迎下单！\n\n🔗 商品直达链接：${shareUrl}`;
    
    if (navigator.share) {
        navigator.share({
            title: '分享商品',
            text: shareText,
            url: shareUrl
        });
    } else {
        copyToClipboard(shareText);
        alert('分享链接已复制到剪贴板');
    }
}
</script>

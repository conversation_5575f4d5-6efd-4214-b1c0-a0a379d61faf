/**
 * 企业级寄售网站主要JavaScript功能
 */

// 全局变量
let currentOrder = null;
let paymentCheckInterval = null;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化模态框
    initializeModal();
    
    // 初始化加载覆盖层
    initializeLoadingOverlay();
    
    // 绑定全局事件
    bindGlobalEvents();
    
    console.log('Mika Shop Web App initialized');
}

/**
 * 初始化模态框
 */
function initializeModal() {
    const modal = document.getElementById('orderModal');
    if (!modal) return;
    
    const closeBtn = modal.querySelector('.close');
    
    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }
    
    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
            closeModal();
        }
    });
}

/**
 * 初始化加载覆盖层
 */
function initializeLoadingOverlay() {
    // 创建加载覆盖层
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loadingOverlay';
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中...</div>
        </div>
    `;
    document.body.appendChild(loadingOverlay);
}

/**
 * 绑定全局事件
 */
function bindGlobalEvents() {
    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * 显示加载状态
 * @param {string} text 加载文本
 */
function showLoading(text = '加载中...') {
    const overlay = document.getElementById('loadingOverlay');
    const textElement = overlay.querySelector('.loading-text');
    
    if (textElement) {
        textElement.textContent = text;
    }
    
    overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    overlay.classList.remove('show');
    document.body.style.overflow = '';
}

/**
 * 显示模态框
 * @param {string} content 模态框内容
 */
function showModal(content) {
    const modal = document.getElementById('orderModal');
    const modalBody = document.getElementById('modalBody');
    
    if (modalBody) {
        modalBody.innerHTML = content;
    }
    
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

/**
 * 关闭模态框
 */
function closeModal() {
    const modal = document.getElementById('orderModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
    
    // 清除支付检查定时器
    if (paymentCheckInterval) {
        clearInterval(paymentCheckInterval);
        paymentCheckInterval = null;
    }
    
    currentOrder = null;
}

/**
 * 显示订单模态框
 * @param {Object} orderData 订单数据
 */
function showOrderModal(orderData) {
    currentOrder = orderData;
    const orderInfo = orderData.order_info;
    const paymentUrl = orderData.payment_url;
    
    const modalContent = `
        <div class="order-info">
            <h3><i class="fas fa-shopping-cart"></i> 订单创建成功</h3>
            <div class="order-details">
                <div class="order-item">
                    <span class="label">订单号:</span>
                    <span class="value">${orderInfo.order_id}</span>
                </div>
                <div class="order-item">
                    <span class="label">商品名称:</span>
                    <span class="value">${orderInfo.product_name}</span>
                </div>
                <div class="order-item">
                    <span class="label">需支付金额:</span>
                    <span class="value">¥${orderInfo.product_price}</span>
                </div>
                <div class="order-item">
                    <span class="label">创建时间:</span>
                    <span class="value">${orderInfo.created_at}</span>
                </div>
                <div class="order-item">
                    <span class="label">购买人ID:</span>
                    <span class="value">${orderInfo.customer_contact}</span>
                </div>
            </div>
            
            <div class="payment-info">
                <h4>请完成支付</h4>
                <div class="payment-qr">
                    <i class="fas fa-qrcode"></i>
                </div>
                <p>请使用对应的支付应用扫描二维码完成支付</p>
            </div>
            
            <div class="payment-actions">
                <a href="${paymentUrl}" class="btn btn-primary" target="_blank">
                    <i class="fas fa-credit-card"></i>
                    立即支付
                </a>
                <button class="btn btn-success" onclick="checkPaymentStatus('${orderInfo.order_id}')">
                    <i class="fas fa-check"></i>
                    我已支付
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    取消
                </button>
            </div>
        </div>
    `;
    
    showModal(modalContent);
}

/**
 * 检查支付状态
 * @param {string} orderId 订单ID
 */
function checkPaymentStatus(orderId) {
    showLoading('检查支付状态...');
    
    fetch('index.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `ajax=1&action=check_payment&order_id=${orderId}`
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.status === 'success') {
            const orderData = data.data;
            if (orderData.order_status === 'paid') {
                showPaymentSuccess(orderData);
            } else {
                showPaymentPending(orderData);
            }
        } else {
            alert('检查支付状态失败: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        alert('网络错误，请稍后重试');
        console.error('Error:', error);
    });
}

/**
 * 显示支付成功
 * @param {Object} orderData 订单数据
 */
function showPaymentSuccess(orderData) {
    const modalContent = `
        <div class="payment-result success">
            <i class="fas fa-check-circle"></i>
            <h3>支付成功！</h3>
            <div class="order-details">
                <div class="order-item">
                    <span class="label">订单号:</span>
                    <span class="value">${orderData.order_id}</span>
                </div>
                <div class="order-item">
                    <span class="label">商品名称:</span>
                    <span class="value">${orderData.product_name}</span>
                </div>
                <div class="order-item">
                    <span class="label">支付金额:</span>
                    <span class="value">¥${orderData.product_price}</span>
                </div>
                <div class="order-item">
                    <span class="label">支付时间:</span>
                    <span class="value">${orderData.updated_at}</span>
                </div>
            </div>
            
            <div class="delivery-info">
                <h4><i class="fas fa-gift"></i> 发货内容</h4>
                <div class="delivery-content">${orderData.delivery_content}</div>
            </div>
            
            <div class="payment-actions">
                <button class="btn btn-primary" onclick="copyDeliveryContent('${orderData.delivery_content}')">
                    <i class="fas fa-copy"></i>
                    复制内容
                </button>
                <a href="${getComplaintUrl()}" class="btn btn-outline" target="_blank">
                    <i class="fas fa-exclamation-triangle"></i>
                    投诉此订单
                </a>
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    关闭
                </button>
            </div>
        </div>
    `;
    
    showModal(modalContent);
}

/**
 * 显示支付待处理
 * @param {Object} orderData 订单数据
 */
function showPaymentPending(orderData) {
    const modalContent = `
        <div class="payment-result error">
            <i class="fas fa-clock"></i>
            <h3>订单未支付</h3>
            <p>请完成支付后再点击"我已支付"按钮</p>
            
            <div class="order-details">
                <div class="order-item">
                    <span class="label">订单号:</span>
                    <span class="value">${orderData.order_id}</span>
                </div>
                <div class="order-item">
                    <span class="label">需支付金额:</span>
                    <span class="value">¥${orderData.product_price}</span>
                </div>
            </div>
            
            <div class="payment-actions">
                <button class="btn btn-success" onclick="checkPaymentStatus('${orderData.order_id}')">
                    <i class="fas fa-check"></i>
                    我已支付
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    关闭
                </button>
            </div>
        </div>
    `;
    
    showModal(modalContent);
}

/**
 * 复制发货内容到剪贴板
 * @param {string} content 内容
 */
function copyDeliveryContent(content) {
    copyToClipboard(content);
    alert('发货内容已复制到剪贴板');
}

/**
 * 复制文本到剪贴板
 * @param {string} text 要复制的文本
 */
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text);
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
    }
}

/**
 * 获取投诉URL
 * @returns {string} 投诉URL
 */
function getComplaintUrl() {
    return 'https://cloudshop.qnm6.top/tousu.html';
}

/**
 * 格式化时间
 * @param {string} datetime 时间字符串
 * @returns {string} 格式化后的时间
 */
function formatDateTime(datetime) {
    const date = new Date(datetime);
    return date.toLocaleString('zh-CN');
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

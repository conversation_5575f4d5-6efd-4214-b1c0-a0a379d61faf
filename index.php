<?php
require_once 'config.php';

// 获取URL参数
$merchant_id = get_param('merchant', '');
$product_id = get_param('product', '');
$action = get_param('action', 'home');

// 处理AJAX请求
if (isset($_POST['ajax'])) {
    handle_ajax_request();
    exit;
}

// 页面数据初始化
$page_data = [
    'merchant_info' => null,
    'products' => [],
    'product_info' => null,
    'error' => null,
    'success' => null
];

// 根据参数加载不同内容
if (!empty($merchant_id)) {
    load_merchant_data($merchant_id, $page_data);
} elseif (!empty($product_id)) {
    load_product_data($product_id, $page_data);
}

/**
 * 加载商户数据
 */
function load_merchant_data($merchant_id, &$page_data) {
    // 获取商户信息
    $merchant_response = get_merchant_info($merchant_id);
    if (!$merchant_response || $merchant_response['status'] !== 'success') {
        $page_data['error'] = '商户不存在或获取商户信息失败';
        return;
    }
    
    $page_data['merchant_info'] = $merchant_response['data'];
    
    // 获取商品列表
    $merchant_secret = generate_merchant_secret($merchant_id);
    $products_response = get_product_list($merchant_secret);
    
    if ($products_response && $products_response['status'] === 'success') {
        $page_data['products'] = $products_response['data'];
    }
}

/**
 * 加载商品数据
 */
function load_product_data($product_id, &$page_data) {
    $product_response = get_product_info($product_id);
    if (!$product_response || $product_response['status'] !== 'success') {
        $page_data['error'] = '获取商品信息失败';
        return;
    }
    
    $page_data['product_info'] = $product_response['data'];
    
    // 同时加载商户信息
    $merchant_id = $page_data['product_info']['merchant_id'];
    if ($merchant_id) {
        load_merchant_data($merchant_id, $page_data);
    }
}

/**
 * 处理AJAX请求
 */
function handle_ajax_request() {
    $action = post_param('action');
    
    switch ($action) {
        case 'create_order':
            handle_create_order();
            break;
        case 'check_payment':
            handle_check_payment();
            break;
        default:
            json_response(['status' => 'error', 'message' => '无效的操作']);
    }
}

/**
 * 处理创建订单
 */
function handle_create_order() {
    $product_id = post_param('product_id');
    $pay_type = post_param('pay_type', 'wxpay');
    $customer_contact = post_param('customer_contact');
    
    if (empty($product_id) || empty($customer_contact)) {
        json_response(['status' => 'error', 'message' => '参数不完整']);
    }
    
    // 检查商品库存
    $product_response = get_product_info($product_id);
    if (!$product_response || $product_response['status'] !== 'success') {
        json_response(['status' => 'error', 'message' => '获取商品信息失败']);
    }
    
    $product_info = $product_response['data'];
    $stock_quantity = intval($product_info['stock_quantity']);
    
    if ($stock_quantity <= 0) {
        json_response(['status' => 'error', 'message' => '商品已售罄']);
    }
    
    // 创建订单
    $order_response = create_order($customer_contact, $product_id, $pay_type);
    
    if (!$order_response || $order_response['status'] !== 'success') {
        $error_message = $order_response['message'] ?? '创建订单失败';
        json_response(['status' => 'error', 'message' => $error_message]);
    }
    
    json_response(['status' => 'success', 'data' => $order_response['data']]);
}

/**
 * 处理支付状态检查
 */
function handle_check_payment() {
    $order_id = post_param('order_id');
    
    if (empty($order_id)) {
        json_response(['status' => 'error', 'message' => '订单ID不能为空']);
    }
    
    $payment_response = check_payment_status($order_id);
    
    if (!$payment_response || $payment_response['status'] !== 'success') {
        json_response(['status' => 'error', 'message' => '检查支付状态失败']);
    }
    
    json_response(['status' => 'success', 'data' => $payment_response['data']]);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?></title>
    <meta name="description" content="<?php echo SITE_DESCRIPTION; ?>">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-store"></i>
                    <?php echo SITE_NAME; ?>
                </h1>
                <nav class="nav">
                    <a href="index.php" class="nav-link">首页</a>
                    <a href="#about" class="nav-link">关于我们</a>
                    <a href="<?php echo COMPLAINT_URL; ?>" class="nav-link" target="_blank">投诉建议</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
        <div class="container">
            <?php if ($page_data['error']): ?>
                <!-- 错误信息 -->
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p><?php echo $page_data['error']; ?></p>
                    <a href="index.php" class="btn btn-primary">返回首页</a>
                </div>
            
            <?php elseif ($page_data['product_info']): ?>
                <!-- 商品详情页面 -->
                <?php include 'templates/product-detail.php'; ?>
            
            <?php elseif ($page_data['merchant_info']): ?>
                <!-- 商户店铺页面 -->
                <?php include 'templates/merchant-shop.php'; ?>
            
            <?php else: ?>
                <!-- 首页 -->
                <?php include 'templates/homepage.php'; ?>
            
            <?php endif; ?>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>关于我们</h3>
                    <p><?php echo SITE_DESCRIPTION; ?></p>
                </div>
                <div class="footer-section">
                    <h3>联系方式</h3>
                    <p><i class="fab fa-telegram"></i> @<?php echo BOT_USERNAME; ?></p>
                    <p><i class="fas fa-envelope"></i> 官方通知频道: @MikaJiShou8</p>
                </div>
                <div class="footer-section">
                    <h3>服务特色</h3>
                    <ul>
                        <li>7x24h自动发货</li>
                        <li>交易完全匿名</li>
                        <li>零门槛入驻</li>
                        <li>售后稳定可靠</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 <?php echo SITE_NAME; ?>. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- 模态框 -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
